import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import IconButton from "./IconButton";
import { IconType } from "../Icon/Icons";

const meta: Meta<typeof IconButton> = {
  title: "General/Buttons/IconButton",
  component: IconButton,
  tags: ["autodocs", "!dev"],
  args: {
    onClick: () => console.log("IconButton clicked"),
    state: "default",
    size: "big",
    iconType: "close",
    ariaLabel: "Cerrar",
  },
  argTypes: {
    iconType: {
      control: {
        type: "select",
        options: [
          "home",
          "play",
          "pause",
          "musicOn",
          "musicOff",
          "soundOn",
          "soundOff",
          "aura",
          "reload",
          "mic",
          "menu",
          "thumbUp",
          "thumbDown",
          "thumbUpFilled",
          "thumbDownFilled",
          "close",
          "addMore",
          "back",
        ] as IconType[],
      },
    },
    children: {
      control: false,
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "<Icon />" },
      },
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          "Botón de icono genérico. Puedes pasar un `iconType` o un `children` personalizado.",
      },
    },
    layout: "centered",
    backgrounds: {
      default: "dark-blue",
      values: [{ name: "dark-blue", value: "#061824" }],
    },
  },
};

export default meta;
type Story = StoryObj<typeof IconButton>;

export const Default: Story = {
  name: "Default",
};
